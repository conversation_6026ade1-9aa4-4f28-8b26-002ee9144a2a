"use client";

import { useState, useMemo } from "react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

import {
  servicesData,
  serviceCategories,
  type ServiceCategoryType,
} from "../data";
import ServicePreviewDialog from "./ServicePreviewDialog";
import ServiceImgCard from "./ServiceImgCard";

type Props = {
  className?: string;
};

const ServicesSection = ({ className }: Props) => {
  const t = useTranslations("services");
  const [activeCategory, setActiveCategory] =
    useState<ServiceCategoryType>("all");

  const filteredServices = useMemo(() => {
    if (activeCategory === "all") {
      return servicesData;
    }
    return servicesData.filter((item) => item.category === activeCategory);
  }, [activeCategory, servicesData]);

  return (
    <section className={cn("bg-neutral flex flex-col gap-5", className)}>
      <div className={cn("flex gap-10 max-sm:flex-col")}>
        <header className="flex flex-1 flex-col gap-10">
          <h2 className="text-foreground text-4xl font-bold sm:text-5xl">
            {t("title")}
          </h2>
          <p className="text-muted-foreground text-lg sm:text-xl">
            {t.rich("description", {
              strong: (chunks) => (
                <strong className="font-bold">{chunks}</strong>
              ),
            })}
          </p>

          <span className="flex flex-wrap gap-2.5">
            {serviceCategories.map((category) => (
              <Button
                key={category}
                variant="outline"
                onClick={() => setActiveCategory(category)}
                aria-pressed={activeCategory === category}
                data-active={activeCategory === category}
                className={cn(
                  "hover:bg-background bg-transparent",
                  category === "audiovisual" && "text-premium",
                  activeCategory === category && "text-primary border-primary",
                )}
              >
                {t(`categories.${category}`)}
              </Button>
            ))}
          </span>
        </header>

        <ul className="grid flex-1 grid-cols-2 flex-wrap gap-5">
          {filteredServices.slice(0, 3).map((item) => (
            <li key={item.key} className="first-of-type:col-start-2">
              <ServiceImgCard item={item} />
            </li>
          ))}
        </ul>
      </div>
      <ul className="grid grid-cols-2 gap-5 sm:grid-cols-4">
        {filteredServices.slice(0, 4).map((item) => (
          <li key={item.key}>
            <ServiceImgCard item={item} />
          </li>
        ))}
      </ul>

      <ServicePreviewDialog />
    </section>
  );
};

export default ServicesSection;
