import {
  parseAsString,
  createSearchParamsCache,
  UrlKeys,
  parseAsStringLiteral,
} from "nuqs/server";
import { serviceCategories } from "./data";

export const serviceSearchParams = {
  selectedService: parseAsString.withDefault(""),
  activeCategory: parseAsStringLiteral(serviceCategories).withDefault("all"),
};

export const serviceUrlKeys: UrlKeys<typeof serviceSearchParams> = {
  selectedService: "svc",
  activeCategory: "cat",
};

export const searchParamsCache = createSearchParamsCache(serviceSearchParams);
